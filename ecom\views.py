from django.shortcuts import render,redirect,reverse
from . import forms,models
from django.http import HttpResponseRedirect,HttpResponse
from django.core.mail import send_mail
from django.contrib.auth.models import Group
from django.contrib.auth.decorators import login_required,user_passes_test
from django.contrib import messages
from django.conf import settings

#   Stripe imports
import stripe

#   PAYPAL imports
from paypal.standard.forms import PayPalPaymentsForm
import uuid
from django.urls import reverse

#   Phonepe imports

import jsons
import base64
import requests
import shortuuid
from cryptography.hazmat.primitives import hashes
from django.views.decorators.csrf import csrf_exempt
from cryptography.hazmat.backends import default_backend
from django.http import JsonResponse
import datetime
import json

stripe.api_key = "sk_test_51RQoRBQQ3USwvpS7nCGQ4qInZZgs0pzragwWRZcmLNKVGlBFrSJdVeljLygC0e6jYJ1ZKL0abkkuG1XKeP9XVRhk00toa8tTdN"

def home_view(request):
    products=models.Product.objects.all()
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=0
    if request.user.is_authenticated:
        return HttpResponseRedirect('afterlogin')
    return render(request,'ecom/index.html',{'products':products,'product_count_in_cart':product_count_in_cart})


#for showing login button for admin(by sumit)
def adminclick_view(request):
    if request.user.is_authenticated:
        return HttpResponseRedirect('afterlogin')
    return HttpResponseRedirect('adminlogin')


def customer_signup_view(request):
    userForm=forms.CustomerUserForm()
    customerForm=forms.CustomerForm()
    mydict={'userForm':userForm,'customerForm':customerForm}
    if request.method=='POST':
        userForm=forms.CustomerUserForm(request.POST)
        customerForm=forms.CustomerForm(request.POST,request.FILES)
        if userForm.is_valid() and customerForm.is_valid():
            user=userForm.save()
            user.set_password(user.password)
            user.save()
            customer=customerForm.save(commit=False)
            customer.user=user
            customer.save()
            my_customer_group = Group.objects.get_or_create(name='CUSTOMER')
            my_customer_group[0].user_set.add(user)
        return HttpResponseRedirect('customerlogin')
    return render(request,'ecom/customersignup.html',context=mydict)

#-----------for checking user iscustomer
def is_customer(user):
    return user.groups.filter(name='CUSTOMER').exists()



#---------AFTER ENTERING CREDENTIALS WE CHECK WHETHER USERNAME AND PASSWORD IS OF ADMIN,CUSTOMER
def afterlogin_view(request):
    if is_customer(request.user):
        return redirect('customer-home')
    else:
        return redirect('admin-dashboard')

#---------------------------------------------------------------------------------
#------------------------ ADMIN RELATED VIEWS START ------------------------------
#---------------------------------------------------------------------------------
@login_required(login_url='adminlogin')
def admin_dashboard_view(request):
    # for cards on dashboard
    customercount=models.Customer.objects.all().count()
    productcount=models.Product.objects.all().count()
    ordercount=models.Orders.objects.all().count()

    # for recent order tables
    orders=models.Orders.objects.all()
    ordered_products=[]
    ordered_bys=[]
    for order in orders:
        ordered_product=models.Product.objects.all().filter(id=order.product.id)
        ordered_by=models.Customer.objects.all().filter(id = order.customer.id)
        ordered_products.append(ordered_product)
        ordered_bys.append(ordered_by)

    mydict={
    'customercount':customercount,
    'productcount':productcount,
    'ordercount':ordercount,
    'data':zip(ordered_products,ordered_bys,orders),
    }
    return render(request,'ecom/admin_dashboard.html',context=mydict)


# admin view customer table
@login_required(login_url='adminlogin')
def view_customer_view(request):
    customers=models.Customer.objects.all()
    return render(request,'ecom/view_customer.html',{'customers':customers})

# admin delete customer
@login_required(login_url='adminlogin')
def delete_customer_view(request,pk):
    customer=models.Customer.objects.get(id=pk)
    user=models.User.objects.get(id=customer.user_id)
    user.delete()
    customer.delete()
    return redirect('view-customer')


@login_required(login_url='adminlogin')
def update_customer_view(request,pk):
    customer=models.Customer.objects.get(id=pk)
    user=models.User.objects.get(id=customer.user_id)
    userForm=forms.CustomerUserForm(instance=user)
    customerForm=forms.CustomerForm(request.FILES,instance=customer)
    mydict={'userForm':userForm,'customerForm':customerForm}
    if request.method=='POST':
        userForm=forms.CustomerUserForm(request.POST,instance=user)
        customerForm=forms.CustomerForm(request.POST,instance=customer)
        if userForm.is_valid() and customerForm.is_valid():
            user=userForm.save()
            user.set_password(user.password)
            user.save()
            customerForm.save()
            return redirect('view-customer')
    return render(request,'ecom/admin_update_customer.html',context=mydict)

# admin view the product
@login_required(login_url='adminlogin')
def admin_products_view(request):
    products=models.Product.objects.all()
    return render(request,'ecom/admin_products.html',{'products':products})


# admin add product by clicking on floating button
@login_required(login_url='adminlogin')
def admin_add_product_view(request):
    productForm=forms.ProductForm()
    if request.method=='POST':
        productForm=forms.ProductForm(request.POST, request.FILES)
        if productForm.is_valid():
            productForm.save()
        return HttpResponseRedirect('admin-products')
    return render(request,'ecom/admin_add_products.html',{'productForm':productForm})


@login_required(login_url='adminlogin')
def delete_product_view(request,pk):
    product=models.Product.objects.get(id=pk)
    product.delete()
    return redirect('admin-products')


@login_required(login_url='adminlogin')
def update_product_view(request,pk):
    product=models.Product.objects.get(id=pk)
    productForm=forms.ProductForm(instance=product)
    if request.method=='POST':
        productForm=forms.ProductForm(request.POST,request.FILES,instance=product)
        if productForm.is_valid():
            productForm.save()
            return redirect('admin-products')
    return render(request,'ecom/admin_update_product.html',{'productForm':productForm})


@login_required(login_url='adminlogin')
def admin_view_booking_view(request):
    orders=models.Orders.objects.all()
    ordered_products=[]
    ordered_bys=[]
    for order in orders:
        ordered_product=models.Product.objects.all().filter(id=order.product.id)
        ordered_by=models.Customer.objects.all().filter(id = order.customer.id)
        ordered_products.append(ordered_product)
        ordered_bys.append(ordered_by)
    return render(request,'ecom/admin_view_booking.html',{'data':zip(ordered_products,ordered_bys,orders)})


@login_required(login_url='adminlogin')
def delete_order_view(request,pk):
    order=models.Orders.objects.get(id=pk)
    order.delete()
    return redirect('admin-view-booking')

# for changing status of order (pending,delivered...)
@login_required(login_url='adminlogin')
def update_order_view(request,pk):
    order=models.Orders.objects.get(id=pk)
    orderForm=forms.OrderForm(instance=order)
    if request.method=='POST':
        orderForm=forms.OrderForm(request.POST,instance=order)
        if orderForm.is_valid():
            orderForm.save()
            return redirect('admin-view-booking')
    return render(request,'ecom/update_order.html',{'orderForm':orderForm})


# admin view the feedback
@login_required(login_url='adminlogin')
def view_feedback_view(request):
    feedbacks=models.Feedback.objects.all().order_by('-id')
    return render(request,'ecom/view_feedback.html',{'feedbacks':feedbacks})



#---------------------------------------------------------------------------------
#------------------------ PUBLIC CUSTOMER RELATED VIEWS START ---------------------
#---------------------------------------------------------------------------------
def search_view(request):
    # whatever user write in search box we get in query
    query = request.GET['query']
    products=models.Product.objects.all().filter(name__icontains=query)
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=0

    # word variable will be shown in html when user click on search button
    word="Searched Result :"

    if request.user.is_authenticated:
        return render(request,'ecom/customer_home.html',{'products':products,'word':word,'product_count_in_cart':product_count_in_cart})
    return render(request,'ecom/index.html',{'products':products,'word':word,'product_count_in_cart':product_count_in_cart})


# any one can add product to cart, no need of signin
def add_to_cart_view(request,pk):
    products=models.Product.objects.all()

    #for cart counter, fetching products ids added by customer from cookies
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=1

    response = render(request, 'ecom/index.html',{'products':products,'product_count_in_cart':product_count_in_cart})

    #adding product id to cookies
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids=="":
            product_ids=str(pk)
        else:
            product_ids=product_ids+"|"+str(pk)
        response.set_cookie('product_ids', product_ids)
    else:
        response.set_cookie('product_ids', pk)

    product=models.Product.objects.get(id=pk)
    messages.info(request, product.name + ' added to cart successfully!')

    return response



# for checkout of cart
def cart_view(request):
    #for cart counter
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=0

    # fetching product details from db whose id is present in cookie
    products=None
    total=0
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids != "":
            product_id_in_cart=product_ids.split('|')
            products=models.Product.objects.all().filter(id__in = product_id_in_cart)

            #for total price shown in cart
            for p in products:
                total=total+p.price
    return render(request,'ecom/cart.html',{'products':products,'total':total,'product_count_in_cart':product_count_in_cart})


def remove_from_cart_view(request,pk):
    #for counter in cart
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=0

    # removing product id from cookie
    total=0
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        product_id_in_cart=product_ids.split('|')
        product_id_in_cart=list(set(product_id_in_cart))
        product_id_in_cart.remove(str(pk))
        products=models.Product.objects.all().filter(id__in = product_id_in_cart)
        #for total price shown in cart after removing product
        for p in products:
            total=total+p.price

        #  for update coookie value after removing product id in cart
        value=""
        for i in range(len(product_id_in_cart)):
            if i==0:
                value=value+product_id_in_cart[0]
            else:
                value=value+"|"+product_id_in_cart[i]
        response = render(request, 'ecom/cart.html',{'products':products,'total':total,'product_count_in_cart':product_count_in_cart})
        if value=="":
            response.delete_cookie('product_ids')
        response.set_cookie('product_ids',value)
        return response


def send_feedback_view(request):
    feedbackForm=forms.FeedbackForm()
    if request.method == 'POST':
        feedbackForm = forms.FeedbackForm(request.POST)
        if feedbackForm.is_valid():
            feedbackForm.save()
            return render(request, 'ecom/feedback_sent.html')
    return render(request, 'ecom/send_feedback.html', {'feedbackForm':feedbackForm})


#---------------------------------------------------------------------------------
#------------------------ CUSTOMER RELATED VIEWS START ------------------------------
#---------------------------------------------------------------------------------
@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def customer_home_view(request):
    products=models.Product.objects.all()
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=0
    return render(request,'ecom/customer_home.html',{'products':products,'product_count_in_cart':product_count_in_cart})



# shipment address before placing order
@login_required(login_url='customerlogin')
def customer_address_view(request):
    # this is for checking whether product is present in cart or not
    # if there is no product in cart we will not show address form
    product_in_cart=False
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids != "":
            product_in_cart=True
    #for counter in cart
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter=product_ids.split('|')
        product_count_in_cart=len(set(counter))
    else:
        product_count_in_cart=0

    addressForm = forms.AddressForm()
    if request.method == 'POST':
        addressForm = forms.AddressForm(request.POST)
        if addressForm.is_valid():
            # here we are taking address, email, mobile at time of order placement
            # we are not taking it from customer account table because
            # these thing can be changes
            email = addressForm.cleaned_data['Email']
            mobile=addressForm.cleaned_data['Mobile']
            address = addressForm.cleaned_data['Address']
            #for showing total price on payment page.....accessing id from cookies then fetching  price of product from db
            
            priceDetails = calculateCheckOutPrice(request)
            
            total = priceDetails["total"]
            products = priceDetails["products"]
                
            line_items = []        
            for product in products:
                line_items.append({
                    'price_data': {
                        'currency': 'inr',
                        'unit_amount': product.price * 100,
                        'product_data': {
                            'name': product.name,
                            'description':product.description,
                        },
                    },
                    'quantity': 1,
                })
            
            response = render(request, 'ecom/payment.html', {'total': total})
            response.set_cookie('email', email)
            response.set_cookie('mobile', mobile)
            response.set_cookie('address', address)
            return response
    return render(request,'ecom/customer_address.html',{'addressForm':addressForm,'product_in_cart':product_in_cart,'product_count_in_cart':product_count_in_cart})




# here we are just directing to this view...actually we have to check whther payment is successful or not
#then only this view should be accessed
@csrf_exempt
def payment_success_view(request):
    # Here we will place order | after successful payment
    # we will fetch customer  mobile, address, Email
    # we will fetch product id from cookies then respective details from db
    # then we will create order objects and store in db
    # after that we will delete cookies because after order placed...cart should be empty
    
    print("Payment Success - GET params:", request.GET)
    print("Payment Success - POST params:", request.POST)

    customer = None
    if request.user.is_authenticated:
        try:
            customer = models.Customer.objects.get(user_id=request.user.id)
            print("got customer id: " ,request.user.id)
        except models.Customer.DoesNotExist:
            pass

    products = None
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids != "":
            product_id_in_cart = product_ids.split('|')
            products = models.Product.objects.all().filter(id__in=product_id_in_cart)

    email = request.COOKIES.get('email', None)
    mobile = request.COOKIES.get('mobile', None)
    address = request.COOKIES.get('address', None)

    # For Stripe payments, try to get data from session metadata
    # session_id = request.GET.get('stripe-session_id')
    # print("session id: " , session_id)
    # if request.GET.get('stripe-session_id'):
    #     try:
    #         session_id = request.GET.get('stripe-session_id')
    #         print("session id: " , session_id)
    #         session = stripe.checkout.Session.retrieve(session_id)
    #         if session.metadata:
    #             print(session.metadata)
    #             email = session.metadata.get('user_email')
    #             mobile = session.metadata.get('phone_number')
    #             address = session.metadata.get('address')
    #     except Exception as e:
    #         print(f"Error retrieving Stripe session: {e}")

    order_id = None
    if products:
        order_id = str(uuid.uuid4())
        for product in products:
            try:
                models.Orders.objects.get_or_create(
                    customer=customer,
                    product=product,
                    status='Pending',
                    email=email,
                    mobile=mobile,
                    address=address,
                    order_id=order_id
                )
                print(f"Order created for product: {product.name}")
            except Exception as e:
                log_payment_error(request.GET.get('payment_type', 'unknown'),
                                f'Order creation failed for {product.name}: {str(e)}',
                                request, order_id=order_id)
    else:
        log_payment_error(request.GET.get('payment_type', 'unknown'),
                         'No products in cart', request)

    # if request.GET.get('payment_type') == "stripe":
        # id = request.GET.get('id')
        # amount = request.GET.get('amount')
        # name = request.GET.get('name')
        # email = request.GET.get('email')
        
        # data = {
        #     "stripe_id": id,
        #     "amount": float(amount)/100,
        #     "name": name,
        #     "email": email
        # }
        
        print("Updating Payments")
        updatePaymentTransaction(request.GET , request.user.id , order_id)
    
    
    res = render(request, 'ecom/payment_success.html')
    res.delete_cookie('product_ids')
    res.delete_cookie('email')
    res.delete_cookie('mobile')
    res.delete_cookie('address')
    
    return res


def updatePaymentTransaction(queryParams, userId, order_id):
    payment_type = queryParams.get('payment_type')
    amount = queryParams.get('amount')

    try:
        models.PaymentLogs.objects.create(
            payment_type=payment_type,
            user_id=userId,
            order_id=order_id,
            amount=amount
        )

        if payment_type == "stripe":
            updateStripeLogs({
                "stripe_id": queryParams.get('id'),
                "amount": float(amount)/100,
                "name": queryParams.get('name'),
                "email": queryParams.get('email')
            })
        elif payment_type == "phonepe":
            updatePhonepeLogs({
                "merchantId": queryParams.get('merchantId'),
                "payment_status": queryParams.get('code'),
                "transactionId": queryParams.get('transactionId'),
                "providerReferenceId": queryParams.get('providerReferenceId'),
                "checksum": queryParams.get('checksum'),
                "amount": amount
            })
        elif payment_type == "google-pay":
            updateGooglepayLogs({
                "token_id": queryParams.get('token_id'),
                "card_last4": queryParams.get('card_last4'),
                "address_city": queryParams.get('address_city'),
                "address_country": queryParams.get('address_country'),
                "address_state": queryParams.get('address_state'),
                "address_zip": queryParams.get('address_zip'),
                "card_brand": queryParams.get('card_brand'),
                "amount": amount
            })

        print("Payment updated")

    except Exception as e:
        log_payment_error(payment_type, f'Payment transaction update failed: {str(e)}',
                         user_id=userId, order_id=order_id, amount=amount)
        print(f"Error updating payment transaction: {str(e)}")


def updateStripeLogs(data):
    try:
        models.StripeLogs.objects.create(**data)
        print("Stripe Log created")
    except Exception as e:
        log_payment_error('stripe', f'Stripe log creation failed: {str(e)}', amount=data.get('amount'))
        print(f"Error creating Stripe log: {str(e)}")

def updatePhonepeLogs(data):
    try:
        models.PhonepeLogs.objects.create(**data)
        print("PhonePe Log created")
    except Exception as e:
        log_payment_error('phonepe', f'PhonePe log creation failed: {str(e)}', amount=data.get('amount'))
        print(f"Error creating PhonePe log: {str(e)}")

def updateGooglepayLogs(data):
    try:
        models.GooglepayLogs.objects.create(**data)
        print("Google Pay Log created")
    except Exception as e:
        log_payment_error('google-pay', f'Google Pay log creation failed: {str(e)}', amount=data.get('amount'))
        print(f"Error creating Google Pay log: {str(e)}")

@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def my_order_view(request):
    customer=models.Customer.objects.get(user_id=request.user.id)
    orders=models.Orders.objects.all().filter(customer_id = customer)
    ordered_products=[]
    for order in orders:
        ordered_product=models.Product.objects.all().filter(id=order.product.id)
        ordered_products.append(ordered_product)

    return render(request,'ecom/my_order.html',{'data':zip(ordered_products,orders)})




#--------------for discharge patient bill (pdf) download and printing
import io
from xhtml2pdf import pisa
from django.template.loader import get_template
from django.http import HttpResponse


def render_to_pdf(template_src, context_dict):
    template = get_template(template_src)
    html  = template.render(context_dict)
    result = io.BytesIO()
    pdf = pisa.pisaDocument(io.BytesIO(html.encode("ISO-8859-1")), result)
    if not pdf.err:
        return HttpResponse(result.getvalue(), content_type='application/pdf')
    return

@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def download_invoice_view(request,orderID,productID):
    order=models.Orders.objects.get(id=orderID)
    product=models.Product.objects.get(id=productID)
    mydict={
        'orderDate':order.order_date,
        'customerName':request.user,
        'customerEmail':order.email,
        'customerMobile':order.mobile,
        'shipmentAddress':order.address,
        'orderStatus':order.status,

        'productName':product.name,
        'productImage':product.product_image,
        'productPrice':product.price,
        'productDescription':product.description,


    }
    return render_to_pdf('ecom/download_invoice.html',mydict)






@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def my_profile_view(request):
    customer=models.Customer.objects.get(user_id=request.user.id)
    return render(request,'ecom/my_profile.html',{'customer':customer})


@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def edit_profile_view(request):
    customer=models.Customer.objects.get(user_id=request.user.id)
    user=models.User.objects.get(id=customer.user_id)
    userForm=forms.CustomerUserForm(instance=user)
    customerForm=forms.CustomerForm(request.FILES,instance=customer)
    mydict={'userForm':userForm,'customerForm':customerForm}
    if request.method=='POST':
        userForm=forms.CustomerUserForm(request.POST,instance=user)
        customerForm=forms.CustomerForm(request.POST,instance=customer)
        if userForm.is_valid() and customerForm.is_valid():
            user=userForm.save()
            user.set_password(user.password)
            user.save()
            customerForm.save()
            return HttpResponseRedirect('my-profile')
    return render(request,'ecom/edit_profile.html',context=mydict)



#---------------------------------------------------------------------------------
#------------------------ ABOUT US AND CONTACT US VIEWS START --------------------
#---------------------------------------------------------------------------------
def aboutus_view(request):
    return render(request,'ecom/aboutus.html')

def contactus_view(request):
    sub = forms.ContactusForm()
    if request.method == 'POST':
        sub = forms.ContactusForm(request.POST)
        if sub.is_valid():
            email = sub.cleaned_data['Email']
            name=sub.cleaned_data['Name']
            message = sub.cleaned_data['Message']
            send_mail(str(name)+' || '+str(email),message, settings.EMAIL_HOST_USER, settings.EMAIL_RECEIVING_USER, fail_silently = False)
            return render(request, 'ecom/contactussuccess.html')
    return render(request, 'ecom/contactus.html', {'form':sub})


def calculateCheckOutPrice(request):
    total=0
    products=None
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids != "":
            product_id_in_cart=product_ids.split('|')
            products=models.Product.objects.all().filter(id__in = product_id_in_cart)
            for p in products:
                total=total+p.price
    
    return {
        'total': total,
        'products': products,
    }


def calculate_sha256_string(input_string):
    sha256 = hashes.Hash(hashes.SHA256(), backend=default_backend())
    sha256.update(input_string.encode('utf-8'))
    return sha256.finalize().hex()

def base64_encode(input_dict):
    json_data = jsons.dumps(input_dict)
    data_bytes = json_data.encode('utf-8')
    return base64.b64encode(data_bytes).decode('utf-8')

def stripe_payment(request):
    amount_data = calculateCheckOutPrice(request)
    amount = amount_data["total"]
    products = amount_data["products"]

    if amount <= 0:
        log_payment_error('stripe', 'Cart is empty', request, amount=amount)
        messages.error(request, "Your cart is empty. Please add products to cart before making payment.")
        return redirect('cart')

    # Get customer data from cookies
    email = request.COOKIES.get('email', None)
    mobile = request.COOKIES.get('mobile', None)
    address = request.COOKIES.get('address', None)

    if not email or not mobile or not address:
        log_payment_error('stripe', 'Customer information missing', request, amount=amount)
        messages.error(request, "Customer information missing. Please fill address form again.")
        return redirect('customer-address')

    line_items = []
    for product in products:
        line_items.append({
            'price_data': {
                'currency': 'inr',
                'unit_amount': product.price * 100,
                'product_data': {
                    'name': product.name,
                    'description': product.description,
                },
            },
            'quantity': 1,
        })

    try:
        stripe.Customer.create()
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=line_items,
            metadata={'user_email': email, 'phone_number': mobile, 'address': address},
            mode='payment',
            success_url = "http://127.0.0.1:8000/validate-payment?stripe-session_id={CHECKOUT_SESSION_ID}",
            cancel_url='http://127.0.0.1:8000/cart',
        )
        return redirect(checkout_session.url)

    except Exception as e:
        log_payment_error('stripe', f'Stripe payment failed: {str(e)}', request, amount=amount)
        messages.error(request, f"Stripe payment failed: {str(e)}")
        return redirect('cart')


def phonepe_payment(request):

    amount_data = calculateCheckOutPrice(request)
    amount = amount_data["total"]

    if amount <= 0:
        log_payment_error('phonepe', 'Cart is empty', request, amount=amount)
        messages.error(request, "Your cart is empty. Please add products to cart before making payment.")
        return redirect('cart')

    email = request.COOKIES.get('email', None)
    mobile = request.COOKIES.get('mobile', None)
    address = request.COOKIES.get('address', None)

    if not email or not mobile or not address:
        log_payment_error('phonepe', 'Customer information missing', request, amount=amount)
        messages.error(request, "Customer information missing. Please fill address form again.")
        return redirect('customer-address')

    try:
        transaction_id = shortuuid.uuid()
        MAINPAYLOAD = {
            "merchantId": "PGTESTPAYUAT86",
            "merchantTransactionId": transaction_id,
            "merchantUserId": f"MUID{request.user.id if request.user.is_authenticated else 'GUEST'}",
            "amount": int(amount * 100),
            "redirectUrl": request.build_absolute_uri('/validate-payment/'),
            "redirectMode": "POST",
            "callbackUrl": request.build_absolute_uri('/cart'),
            "mobileNumber": "9999999999",
            "paymentInstrument": {"type": "PAY_PAGE"}
        }

        base64String = base64_encode(MAINPAYLOAD)
        mainString = base64String + "/pg/v1/pay" + "*************-489d-8924-ab56988a6076"
        sha256Val = calculate_sha256_string(mainString)
        checkSum = sha256Val + '###1'

        response = requests.post(
            'https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay',
            headers={'Content-Type': 'application/json', 'X-VERIFY': checkSum, 'accept': 'application/json'},
            json={'request': base64String},
            timeout=30
        )

        responseData = response.json()
        if responseData.get('success') and 'data' in responseData and 'instrumentResponse' in responseData['data']:
            return redirect(responseData['data']['instrumentResponse']['redirectInfo']['url'])
        else:
            log_payment_error('phonepe', f'PhonePe payment failed: {responseData.get("message", "Unknown error")}', request, amount=amount)
            messages.error(request, "PhonePe payment initiation failed. Please try again.")
            return redirect('cart')

    except Exception as e:
        log_payment_error('phonepe', f'PhonePe payment error: {str(e)}', request, amount=amount)
        messages.error(request, "Failed to initialize payment. Please try again.")
        return redirect('cart')


@csrf_exempt
def gpay_payment(request):

    if request.method != "POST":
        log_payment_error('google-pay', 'Invalid request method', request)
        return JsonResponse({"error": "Invalid request method"}, status=400)

    try:
        payment_data = request.POST.get("paymentData")
        if not payment_data:
            log_payment_error('google-pay', 'Missing payment data', request)
            return JsonResponse({"error": "Missing payment data"}, status=400)

        amount_data = calculateCheckOutPrice(request)
        amount = amount_data["total"]

        if amount <= 0:
            log_payment_error('google-pay', 'Cart is empty', request, amount=amount)
            return JsonResponse({"error": "Invalid amount"}, status=400)

        payment_json = jsons.loads(payment_data)
        token_json = jsons.loads(payment_json['paymentMethodData']['tokenizationData']['token'])
        stripe_token = token_json['id']

        charge = stripe.Charge.create(
            amount=int(amount * 100),
            currency='inr',
            description='Google Pay Demo Payment',
            source=stripe_token
        )

        if charge.status == "succeeded":
            url = construct_redirect_url("google-pay", payment_json, settings.HOST+"payment-success", amount)
            return redirect(url)
        else:
            log_payment_error('google-pay', f'Payment failed with status: {charge.status}', request, amount=amount)
            return JsonResponse({"error": "Payment failed."}, status=400)

    except Exception as e:
        log_payment_error('google-pay', f'Google Pay error: {str(e)}', request)
        return JsonResponse({"error": str(e)}, status=400)
    

@csrf_exempt
def validate_payment(request):
    print("Validate GET params:", request.GET)
    print("Validate POST params:", request.POST)
    
    if request.GET.get('stripe-session_id'):
        session_id = request.GET.get('stripe-session_id')
        # print("session id: " , session_id)
        session = stripe.checkout.Session.retrieve(session_id)
        url = construct_redirect_url("stripe" , session , settings.HOST+"payment-success")
        return redirect(url)
    
    # print(request.POST.get('merchantId'))
    if request.POST.get('merchantId'):
        url = construct_redirect_url("phonepe" , request.POST , settings.HOST+"payment-success")
        
        return redirect(url)
    # return HttpResponse("<h1>Validate Payment View</h1>")

def construct_redirect_url(payment_type , data , redirect_url , gpay_amount=None):
    url = redirect_url
    if payment_type == "google-pay":
        #   ?key=value
        tokenizationData = jsons.loads(data['paymentMethodData']['tokenizationData']['token'])
        token_id = tokenizationData['id']
        card_last4 = tokenizationData['card']['dynamic_last4']
        address_city = tokenizationData['card']['address_city']
        address_country = tokenizationData['card']['address_country']
        address_state = tokenizationData['card']['address_state']
        address_zip = tokenizationData['card']['address_zip']
        card_brand = tokenizationData['card']['brand']
        
        url += f"?payment_type={"google-pay"}&amount={gpay_amount}&token_id={token_id}&card_last4={card_last4}&address_city={address_city}&address_country={address_country}&address_state={address_state}&address_zip={address_zip}&card_brand={card_brand}"
        
        return url
    
    elif payment_type == "stripe":
        id = data['id']
        amount = data['amount_total']
        name = data['customer_details']['name']
        email = data['customer_details']['email']
        
        url += f"?payment_type={"stripe"}&id={id}&amount={amount}&email={email}&name={name}"
        
        return url
    
    elif payment_type == "phonepe":
        code = data.get('code')
        merchantId = data.get('merchantId')
        transactionId = data.get('transactionId')
        amount = data.get('amount')
        providerReferenceId = data.get('providerReferenceId')
        checksum  = data.get('checksum')
        
        url += f"?payment_type={"phonepe"}&code={code}&merchantId={merchantId}&transactionId={transactionId}&amount={amount}&providerReferenceId={providerReferenceId}&checksum={checksum}"

        return url
    

def log_payment_error(payment_type, error_message, request=None, **kwargs):
    try:
        models.PaymentErrorLogs.objects.create(
            payment_type=payment_type,
            error_message=error_message,
            user_id=request.user.id if request and request.user.is_authenticated else None,
            order_id=kwargs.get('order_id'),
            amount=kwargs.get('amount'),
            customer_email=request.COOKIES.get('email') if request else kwargs.get('customer_email')
        )
        print(f"Payment error logged: {payment_type} - {error_message}")
    except Exception as e:
        print(f"Failed to log error: {e}")