# # 1

# def construct_redirect_url(payment_type , data):
#     url = None
#     if payment_type == "phonepe":
#         url = data['data']['instrumentResponse']['redirectInfo']['url']
#         #   ?key=value
#         url += f"merchantId={data['data']['merchantId']}&merchantTransactionId={data['data']['merchantTransactionId']}"
#         return url

# data = {
#     'success': True, 
#     'code': 'PAYMENT_INITIATED', 
#     'message': 'Payment initiated', 
#     'data': {
#         'merchantId': 'PGTESTPAYUAT86', 
#         'merchantTransactionId': 'FqJQYrixj5zWW5xPLZ2vn3',
#          'instrumentResponse': {
#             'type': 'PAY_PAGE', 
#             'redirectInfo': {
#                 'url': 'https://mercury-uat.phonepe.com/transact/simulator?token=BtYkHF0NC3COl8nsQSrYDNbdfnOKLNPviso8vLgBj9ZNVtuCeY', 
#                 'method': 'GET'
#             }
#         }
#     }
# }

# print(construct_redirect_url("phonepe" , data))

import stripe
stripe.api_key = "sk_test_51RQoRBQQ3USwvpS7nCGQ4qInZZgs0pzragwWRZcmLNKVGlBFrSJdVeljLygC0e6jYJ1ZKL0abkkuG1XKeP9XVRhk00toa8tTdN"

session_id = 'cs_test_a16Nqj1WzOZJxHea4oSNd4vyayVRhZN3yZlUXW4QkFcr7OdJmZMCGQxnkK'
session = stripe.checkout.Session.retrieve(session_id)
print(session)

{
  "adaptive_pricing": {
    "enabled": true
  },
  "after_expiration": null,
  "allow_promotion_codes": null,
  "amount_subtotal": 25000000,
  "amount_total": 25000000,
  "automatic_tax": {
    "enabled": false,
    "liability": null,
    "provider": null,
    "status": null
  },
  "billing_address_collection": null,
  "cancel_url": "http://127.0.0.1:8000/cart",
  "client_reference_id": null,
  "client_secret": null,
  "collected_information": null,
  "consent": null,
  "consent_collection": null,
  "created": **********,
  "currency": "inr",
  "currency_conversion": null,
  "custom_fields": [],
  "custom_text": {
    "after_submit": null,
    "shipping_address": null,
    "submit": null,
    "terms_of_service_acceptance": null
  },
  "customer": null,
  "customer_creation": "if_required",
  "customer_details": {
    "address": {
      "city": null,
      "country": "IN",
      "line1": null,
      "line2": null,
      "postal_code": null,
      "state": null
    },
    "email": "<EMAIL>",
    "name": "Test",
    "phone": null,
    "tax_exempt": "none",
    "tax_ids": []
  },
  "customer_email": null,
  "discounts": [],
  "expires_at": **********,
  "id": "cs_test_a16Nqj1WzOZJxHea4oSNd4vyayVRhZN3yZlUXW4QkFcr7OdJmZMCGQxnkK",       
  "invoice": null,
  "invoice_creation": {
    "enabled": false,
    "invoice_data": {
      "account_tax_ids": null,
      "custom_fields": null,
      "description": null,
      "footer": null,
      "issuer": null,
      "metadata": {},
      "rendering_options": null
    }
  },
  "livemode": false,
  "locale": null,
  "metadata": {
    "address": "aasd",
    "phone_number": "**********",
    "user_email": "<EMAIL>"
  },
  "mode": "payment",
  "object": "checkout.session",
  "payment_intent": "pi_3RVPBEQQ3USwvpS70eVAtfvD",
  "payment_link": null,
  "payment_method_collection": "if_required",
  "payment_method_configuration_details": null,
  "payment_method_options": {
    "card": {
      "request_three_d_secure": "automatic"
    }
  },
  "payment_method_types": [
    "card"
  ],
  "payment_status": "paid",
  "permissions": null,
  "phone_number_collection": {
    "enabled": false
  },
  "recovered_from": null,
  "saved_payment_method_options": null,
  "setup_intent": null,
  "shipping_address_collection": null,
  "shipping_cost": null,
  "shipping_options": [],
  "status": "complete",
  "submit_type": null,
  "subscription": null,
  "success_url": "http://127.0.0.1:8000/payment-success?session_id={CHECKOUT_SESSION_ID}",
  "total_details": {
    "amount_discount": 0,
    "amount_shipping": 0,
    "amount_tax": 0
  },
  "ui_mode": "hosted",
  "url": null,
  "wallet_options": null
}