<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Django E-Commerce: Technical Implementation Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #e74c3c;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        h4 {
            color: #34495e;
            margin-top: 20px;
        }
        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .code-inline {
            background-color: #f1f5f9;
            color: #1e293b;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 13px;
        }
        .highlight {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .concept-box {
            background-color: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .implementation-box {
            background-color: #f0fdf4;
            border: 2px solid #22c55e;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fef2f2;
            border: 2px solid #ef4444;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .flow-diagram {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        .toc {
            background-color: #f8fafc;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #e2e8f0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
        }
        .toc a {
            text-decoration: none;
            color: #3b82f6;
            font-weight: 500;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .syntax-highlight .keyword { color: #c792ea; }
        .syntax-highlight .string { color: #c3e88d; }
        .syntax-highlight .comment { color: #546e7a; }
        .syntax-highlight .function { color: #82aaff; }
        .syntax-highlight .class { color: #ffcb6b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Django E-Commerce: Technical Implementation Guide</h1>
        <p style="text-align: center; font-style: italic; color: #6b7280;">
            A comprehensive guide to Django concepts and their implementation in the e-commerce project
        </p>

        <div class="toc">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#mvc-pattern">1. MVC/MVT Architecture Implementation</a></li>
                <li><a href="#models-orm">2. Models & ORM Implementation</a></li>
                <li><a href="#views-logic">3. Views & Business Logic</a></li>
                <li><a href="#templates-frontend">4. Templates & Frontend Integration</a></li>
                <li><a href="#forms-validation">5. Forms & Data Validation</a></li>
                <li><a href="#authentication">6. Authentication & Authorization</a></li>
                <li><a href="#session-management">7. Session Management & Cookies</a></li>
                <li><a href="#file-handling">8. File Upload & Static Files</a></li>
                <li><a href="#email-integration">9. Email Integration</a></li>
                <li><a href="#payment-integration">10. Payment Gateway Integration</a></li>
                <li><a href="#admin-interface">11. Admin Interface Customization</a></li>
                <li><a href="#url-routing">12. URL Routing & Navigation</a></li>
                <li><a href="#middleware">13. Middleware Implementation</a></li>
                <li><a href="#security">14. Security Implementation</a></li>
                <li><a href="#performance">15. Performance Optimization</a></li>
            </ul>
        </div>

        <section id="mvc-pattern">
            <h2>1. MVC/MVT Architecture Implementation</h2>

            <div class="concept-box">
                <h3>Django MVT Pattern</h3>
                <p>Django follows the Model-View-Template (MVT) pattern, which is a variation of the traditional MVC pattern:</p>
                <ul>
                    <li><strong>Model:</strong> Handles data and business logic</li>
                    <li><strong>View:</strong> Processes requests and returns responses</li>
                    <li><strong>Template:</strong> Handles presentation layer</li>
                </ul>
            </div>

            <h3>Project Structure Implementation</h3>
            <div class="code-block">
Online_Shopping_Project_Django/
├── ecommerce/              # Project configuration (Controller layer)
│   ├── settings.py         # Global settings
│   ├── urls.py            # Main URL routing
│   └── wsgi.py            # WSGI configuration
├── ecom/                  # Main application
│   ├── models.py          # Model layer (Data)
│   ├── views.py           # View layer (Logic)
│   ├── forms.py           # Form handling
│   └── admin.py           # Admin interface
├── templates/ecom/        # Template layer (Presentation)
└── static/               # Static files (CSS, JS, Images)
            </div>

            <div class="implementation-box">
                <h4>How MVT Works in Our E-commerce Project:</h4>
                <div class="flow-diagram">
                    <strong>Request Flow:</strong><br>
                    URL Request → urls.py → views.py → models.py → Database<br>
                    ↓<br>
                    Database → models.py → views.py → templates → Response
                </div>
            </div>

            <h3>URL Configuration (urls.py)</h3>
            <div class="code-block">
# ecommerce/urls.py - Main URL configuration
from django.contrib import admin
from django.urls import path, include
from ecom import views
from django.contrib.auth.views import LoginView, LogoutView

urlpatterns = [
    # Admin URLs
    path('admin/', admin.site.urls),

    # Public URLs
    path('', views.home_view, name=''),
    path('search', views.search_view, name='search'),
    path('aboutus', views.aboutus_view),
    path('contactus', views.contactus_view, name='contactus'),

    # Authentication URLs
    path('afterlogin', views.afterlogin_view, name='afterlogin'),
    path('logout', LogoutView.as_view(template_name='ecom/logout.html'), name='logout'),

    # Customer URLs
    path('customersignup', views.customer_signup_view),
    path('customerlogin', LoginView.as_view(template_name='ecom/customerlogin.html'), name='customerlogin'),
    path('customer-home', views.customer_home_view, name='customer-home'),

    # Shopping Cart URLs
    path('add-to-cart/&lt;int:pk&gt;', views.add_to_cart_view, name='add-to-cart'),
    path('cart', views.cart_view, name='cart'),
    path('remove-from-cart/&lt;int:pk&gt;', views.remove_from_cart_view, name='remove-from-cart'),

    # Admin Management URLs
    path('admin-dashboard', views.admin_dashboard_view, name='admin-dashboard'),
    path('view-customer', views.view_customer_view, name='view-customer'),
    path('admin-products', views.admin_products_view, name='admin-products'),
]
            </div>

            <div class="highlight">
                <strong>Key Implementation Points:</strong>
                <ul>
                    <li>URL patterns use regular expressions and named groups for parameter passing</li>
                    <li>Named URLs enable reverse URL lookup in templates and views</li>
                    <li>Class-based views (LoginView, LogoutView) for authentication</li>
                    <li>Function-based views for custom business logic</li>
                </ul>
            </div>
        </section>

        <section id="models-orm">
            <h2>2. Models & ORM Implementation</h2>

            <div class="concept-box">
                <h3>Django ORM (Object-Relational Mapping)</h3>
                <p>Django's ORM allows you to interact with the database using Python objects instead of SQL queries. Each model class represents a database table.</p>
            </div>

            <h3>Customer Model Implementation</h3>
            <div class="code-block">
# ecom/models.py
from django.db import models
from django.contrib.auth.models import User

class Customer(models.Model):
    # One-to-One relationship with Django's built-in User model
    user = models.OneToOneField(User, on_delete=models.CASCADE)

    # File upload field with custom upload path
    profile_pic = models.ImageField(
        upload_to='profile_pic/CustomerProfilePic/',
        null=True,
        blank=True
    )

    # Character fields with constraints
    address = models.CharField(max_length=40)
    mobile = models.CharField(max_length=20, null=False)

    # Property methods for computed fields
    @property
    def get_name(self):
        return self.user.first_name + " " + self.user.last_name

    @property
    def get_id(self):
        return self.user.id

    # String representation for admin interface
    def __str__(self):
        return self.user.first_name
            </div>

            <div class="implementation-box">
                <h4>Key ORM Concepts Demonstrated:</h4>
                <ul>
                    <li><strong>Relationships:</strong> OneToOneField extends User model</li>
                    <li><strong>File Handling:</strong> ImageField for profile pictures</li>
                    <li><strong>Properties:</strong> Computed fields using @property decorator</li>
                    <li><strong>Constraints:</strong> Field validation and null/blank options</li>
                </ul>
            </div>

            <h3>Product Model with Advanced Features</h3>
            <div class="code-block">
class Product(models.Model):
    name = models.CharField(max_length=40)
    product_image = models.ImageField(upload_to='product_image/', null=True, blank=True)
    price = models.PositiveIntegerField()  # Ensures positive values only
    description = models.CharField(max_length=40)

    def __str__(self):
        return self.name

    # Custom model methods can be added here
    def get_discounted_price(self, discount_percent):
        return self.price * (1 - discount_percent / 100)

    class Meta:
        ordering = ['name']  # Default ordering
        verbose_name_plural = "Products"  # Admin display name
            </div>

            <h3>Orders Model with Complex Relationships</h3>
            <div class="code-block">
class Orders(models.Model):
    # Choice field implementation
    STATUS = (
        ('Pending', 'Pending'),
        ('Order Confirmed', 'Order Confirmed'),
        ('Out for Delivery', 'Out for Delivery'),
        ('Delivered', 'Delivered'),
    )

    # Foreign Key relationships
    customer = models.ForeignKey('Customer', on_delete=models.CASCADE, null=True)
    product = models.ForeignKey('Product', on_delete=models.CASCADE, null=True)

    # Order details
    email = models.CharField(max_length=50, null=True)
    address = models.CharField(max_length=500, null=True)
    mobile = models.CharField(max_length=20, null=True)

    # Auto-generated timestamp
    order_date = models.DateField(auto_now_add=True, null=True)

    # Choice field with predefined options
    status = models.CharField(max_length=50, null=True, choices=STATUS)

    class Meta:
        ordering = ['-order_date']  # Latest orders first
        verbose_name_plural = "Orders"
            </div>

            <div class="highlight">
                <strong>Database Relationships Explained:</strong>
                <ul>
                    <li><strong>OneToOneField:</strong> Customer extends User (1:1)</li>
                    <li><strong>ForeignKey:</strong> Orders reference Customer and Product (Many:1)</li>
                    <li><strong>on_delete=CASCADE:</strong> Deletes related objects when parent is deleted</li>
                </ul>
            </div>

            <h3>Database Migrations</h3>
            <div class="code-block">
# Migration commands used in the project
python manage.py makemigrations  # Create migration files
python manage.py migrate         # Apply migrations to database

# Migration files created:
# 0001_initial.py - Customer model
# 0002_product.py - Product model
# 0003_orders.py - Orders model
# 0004_feedback.py - Feedback model
# 0005_feedback_date.py - Add date field to Feedback
            </div>
        </section>

        <section id="views-logic">
            <h2>3. Views & Business Logic</h2>

            <div class="concept-box">
                <h3>Django Views</h3>
                <p>Views contain the business logic of your application. They process HTTP requests, interact with models, and return HTTP responses.</p>
            </div>

            <h3>Function-Based Views Implementation</h3>
            <div class="code-block">
# ecom/views.py
from django.shortcuts import render, redirect, reverse
from django.http import HttpResponseRedirect, HttpResponse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import Group
from . import forms, models

def home_view(request):
    """
    Homepage view demonstrating:
    - Model queries
    - Cookie handling
    - Template context
    - User authentication check
    """
    # Query all products from database
    products = models.Product.objects.all()

    # Cookie-based cart counter implementation
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter = product_ids.split('|')
        product_count_in_cart = len(set(counter))
    else:
        product_count_in_cart = 0

    # Redirect authenticated users to dashboard
    if request.user.is_authenticated:
        return HttpResponseRedirect('afterlogin')

    # Render template with context data
    return render(request, 'ecom/index.html', {
        'products': products,
        'product_count_in_cart': product_count_in_cart
    })
            </div>

            <h3>Authentication and Authorization</h3>
            <div class="code-block">
# Custom user type checking
def is_customer(user):
    """Check if user belongs to CUSTOMER group"""
    return user.groups.filter(name='CUSTOMER').exists()

# Decorator-based access control
@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def customer_home_view(request):
    """
    Customer dashboard with access control:
    - @login_required: Ensures user is logged in
    - @user_passes_test: Ensures user is a customer
    """
    products = models.Product.objects.all()

    # Cart counter logic (reusable pattern)
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter = product_ids.split('|')
        product_count_in_cart = len(set(counter))
    else:
        product_count_in_cart = 0

    return render(request, 'ecom/customer_home.html', {
        'products': products,
        'product_count_in_cart': product_count_in_cart
    })
            </div>

            <h3>Complex Business Logic - Shopping Cart</h3>
            <div class="code-block">
def add_to_cart_view(request, pk):
    """
    Shopping cart implementation using cookies:
    - Demonstrates cookie manipulation
    - Product ID storage and retrieval
    - Message framework usage
    """
    products = models.Product.objects.all()

    # Calculate current cart count
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        counter = product_ids.split('|')
        product_count_in_cart = len(set(counter))
    else:
        product_count_in_cart = 1

    # Prepare response
    response = render(request, 'ecom/index.html', {
        'products': products,
        'product_count_in_cart': product_count_in_cart
    })

    # Cookie manipulation logic
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids == "":
            product_ids = str(pk)
        else:
            product_ids = product_ids + "|" + str(pk)
        response.set_cookie('product_ids', product_ids)
    else:
        response.set_cookie('product_ids', pk)

    # User feedback using messages framework
    product = models.Product.objects.get(id=pk)
    messages.info(request, product.name + ' added to cart successfully!')

    return response
            </div>

            <div class="implementation-box">
                <h4>View Patterns Demonstrated:</h4>
                <ul>
                    <li><strong>Decorators:</strong> @login_required, @user_passes_test for access control</li>
                    <li><strong>Cookie Management:</strong> Reading, writing, and manipulating cookies</li>
                    <li><strong>Database Queries:</strong> ORM operations for data retrieval</li>
                    <li><strong>Template Context:</strong> Passing data to templates</li>
                    <li><strong>HTTP Responses:</strong> Redirects, renders, and cookie setting</li>
                </ul>
            </div>
        </section>

        <section id="templates-frontend">
            <h2>4. Templates & Frontend Integration</h2>

            <div class="concept-box">
                <h3>Django Template System</h3>
                <p>Django templates separate presentation from logic using a template language that includes variables, filters, and tags.</p>
            </div>

            <h3>Template Inheritance Structure</h3>
            <div class="code-block">
<!-- Base template structure -->
templates/ecom/
├── homebase.html          # Base for public pages
├── customer_base.html     # Base for customer pages
├── admin_base.html        # Base for admin pages
├── navbar.html           # Navigation component
├── footer.html           # Footer component
└── index.html            # Homepage extending base
            </div>

            <h3>Base Template Implementation</h3>
            <div class="code-block">
<!-- templates/ecom/homebase.html -->
{% load static %}
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="utf-8">
  <title>Online Shopping</title>
</head>
<body>
  <!-- Conditional navigation based on authentication -->
  {% if request.user.is_authenticated %}
    {% include "ecom/customer_navbar.html" %}
  {% else %}
    {% include "ecom/navbar.html" %}
  {% endif %}

  <!-- Content block for child templates -->
  {% block content %}
  {% endblock content %}

  <!-- Include footer component -->
  {% include "ecom/footer.html" %}
</body>
</html>
            </div>

            <h3>Template Tags and Filters</h3>
            <div class="code-block">
<!-- templates/ecom/index.html -->
{% extends 'ecom/homebase.html' %}
{% load static %}
{% block content %}

<!-- Template variables and filters -->
{% if products %}
  <h3 style="text-align:center; color:yellow;">{{ word }}</h3>

  <div class="row">
    {% for p in products %}
      <!-- Product card -->
      <div class="column">
        <div class="container page-wrapper">
          <div class="el-wrapper">
            <div class="box-up">
              <!-- Static file loading -->
              <img class="img" src="{% static p.product_image.url %}"
                   alt="product pic" height="300px" width="300px">
              <div class="img-info">
                <div class="info-inner">
                  <!-- Template variable with styling -->
                  <span style="background-color:#08050a; color:yellow;"
                        class="p-company">{{ p.name }}</span>
                </div>
                <div class="a-size">{{ p.description }}</div>
              </div>
            </div>

            <div class="box-down">
              <!-- URL reverse lookup -->
              <a class="cart" href="{% url 'add-to-cart' p.id %}">
                <!-- Template filter for currency -->
                <span class="price">₱ {{ p.price }}</span>
                <span class="add-to-cart">
                  <span class="txt">Add in cart</span>
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Template logic for layout -->
      {% if forloop.counter|divisibleby:"3" %}
        </div>
        <div class="row">
      {% endif %}
    {% endfor %}
  </div>
{% else %}
  <h3 style="text-align:center; color:yellow;">No Search Found</h3>
{% endif %}

{% endblock content %}
            </div>

            <div class="implementation-box">
                <h4>Template Features Demonstrated:</h4>
                <ul>
                    <li><strong>Template Inheritance:</strong> {% extends %} and {% block %}</li>
                    <li><strong>Static Files:</strong> {% load static %} and {% static %}</li>
                    <li><strong>Template Variables:</strong> {{ variable }} syntax</li>
                    <li><strong>Template Tags:</strong> {% if %}, {% for %}, {% url %}</li>
                    <li><strong>Template Filters:</strong> |divisibleby for layout logic</li>
                    <li><strong>URL Reverse:</strong> {% url 'name' parameter %}</li>
                </ul>
            </div>
        </section>

        <section id="forms-validation">
            <h2>5. Forms & Data Validation</h2>

            <div class="concept-box">
                <h3>Django Forms Framework</h3>
                <p>Django forms handle HTML form generation, data validation, and conversion to Python types automatically.</p>
            </div>

            <h3>ModelForm Implementation</h3>
            <div class="code-block">
# ecom/forms.py
from django import forms
from django.contrib.auth.models import User
from . import models

class CustomerUserForm(forms.ModelForm):
    """
    ModelForm for User model with custom widget
    Demonstrates:
    - ModelForm inheritance
    - Custom widgets
    - Field selection
    """
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'username', 'password']
        widgets = {
            'password': forms.PasswordInput()  # Custom widget for password
        }

class CustomerForm(forms.ModelForm):
    """
    ModelForm for Customer model
    Handles file uploads and validation
    """
    class Meta:
        model = models.Customer
        fields = ['address', 'mobile', 'profile_pic']

class ProductForm(forms.ModelForm):
    """
    Product management form for admin
    """
    class Meta:
        model = models.Product
        fields = ['name', 'price', 'description', 'product_image']
            </div>

            <h3>Custom Form Implementation</h3>
            <div class="code-block">
class AddressForm(forms.Form):
    """
    Custom form not tied to a model
    Demonstrates:
    - Custom form fields
    - Field validation
    - Different field types
    """
    Email = forms.EmailField()  # Built-in email validation
    Mobile = forms.IntegerField()  # Integer validation
    Address = forms.CharField(max_length=500)  # Character field with limit

class ContactusForm(forms.Form):
    """
    Contact form with custom widget
    """
    Name = forms.CharField(max_length=30)
    Email = forms.EmailField()
    Message = forms.CharField(
        max_length=500,
        widget=forms.Textarea(attrs={'rows': 3, 'cols': 30})  # Custom textarea
    )
            </div>

            <h3>Form Processing in Views</h3>
            <div class="code-block">
def customer_signup_view(request):
    """
    Form processing demonstration:
    - Form instantiation
    - POST data handling
    - Form validation
    - Model saving
    - User group assignment
    """
    userForm = forms.CustomerUserForm()
    customerForm = forms.CustomerForm()
    mydict = {'userForm': userForm, 'customerForm': customerForm}

    if request.method == 'POST':
        # Bind forms with POST data
        userForm = forms.CustomerUserForm(request.POST)
        customerForm = forms.CustomerForm(request.POST, request.FILES)

        # Validate both forms
        if userForm.is_valid() and customerForm.is_valid():
            # Save user form
            user = userForm.save()
            user.set_password(user.password)  # Hash password
            user.save()

            # Save customer form with relationship
            customer = customerForm.save(commit=False)
            customer.user = user
            customer.save()

            # Add user to customer group
            my_customer_group = Group.objects.get_or_create(name='CUSTOMER')
            my_customer_group[0].user_set.add(user)

        return HttpResponseRedirect('customerlogin')

    return render(request, 'ecom/customersignup.html', context=mydict)
            </div>

            <div class="highlight">
                <strong>Form Validation Features:</strong>
                <ul>
                    <li><strong>Built-in Validators:</strong> EmailField, IntegerField automatic validation</li>
                    <li><strong>Custom Widgets:</strong> PasswordInput, Textarea with custom attributes</li>
                    <li><strong>File Upload:</strong> Automatic handling of file uploads in forms</li>
                    <li><strong>Model Integration:</strong> ModelForm automatically generates fields from models</li>
                </ul>
            </div>
        </section>

        <section id="authentication">
            <h2>6. Authentication & Authorization</h2>

            <div class="concept-box">
                <h3>Django Authentication System</h3>
                <p>Django provides a robust authentication system with users, groups, permissions, and session management.</p>
            </div>

            <h3>User Registration and Groups</h3>
            <div class="code-block">
# User registration with group assignment
from django.contrib.auth.models import Group, User

def customer_signup_view(request):
    # ... form processing ...

    if userForm.is_valid() and customerForm.is_valid():
        # Create user account
        user = userForm.save()
        user.set_password(user.password)  # Hash password properly
        user.save()

        # Create customer profile
        customer = customerForm.save(commit=False)
        customer.user = user  # Link to user account
        customer.save()

        # Assign user to CUSTOMER group for role-based access
        my_customer_group = Group.objects.get_or_create(name='CUSTOMER')
        my_customer_group[0].user_set.add(user)
            </div>

            <h3>Role-Based Access Control</h3>
            <div class="code-block">
# Custom permission checking
def is_customer(user):
    """Check if user belongs to CUSTOMER group"""
    return user.groups.filter(name='CUSTOMER').exists()

# View-level access control using decorators
@login_required(login_url='customerlogin')
@user_passes_test(is_customer)
def customer_home_view(request):
    """
    This view is only accessible to:
    1. Logged-in users (@login_required)
    2. Users in CUSTOMER group (@user_passes_test)
    """
    # View logic here
    pass

# Admin-only views
@login_required(login_url='adminlogin')
def admin_dashboard_view(request):
    """Admin dashboard with login requirement"""
    # Admin logic here
    pass
            </div>

            <h3>Login/Logout Implementation</h3>
            <div class="code-block">
# URL configuration for authentication
from django.contrib.auth.views import LoginView, LogoutView

urlpatterns = [
    # Customer authentication
    path('customerlogin', LoginView.as_view(
        template_name='ecom/customerlogin.html'
    ), name='customerlogin'),

    # Admin authentication
    path('adminlogin', LoginView.as_view(
        template_name='ecom/adminlogin.html'
    ), name='adminlogin'),

    # Logout with custom template
    path('logout', LogoutView.as_view(
        template_name='ecom/logout.html'
    ), name='logout'),
]

# Post-login redirection logic
def afterlogin_view(request):
    """Redirect users based on their role after login"""
    if is_customer(request.user):
        return redirect('customer-home')
    else:
        return redirect('admin-dashboard')
            </div>
        </section>

        <section id="session-management">
            <h2>7. Session Management & Cookies</h2>

            <div class="concept-box">
                <h3>Session vs Cookies</h3>
                <p>This project demonstrates both session management (for authentication) and cookie-based storage (for shopping cart).</p>
            </div>

            <h3>Cookie-Based Shopping Cart</h3>
            <div class="code-block">
def add_to_cart_view(request, pk):
    """
    Cookie-based cart implementation:
    - Stores product IDs in browser cookies
    - Persists across browser sessions
    - No database storage required
    """
    # Read existing cart from cookies
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids == "":
            product_ids = str(pk)
        else:
            product_ids = product_ids + "|" + str(pk)  # Pipe-separated values
    else:
        product_ids = str(pk)

    # Create response and set cookie
    response = render(request, 'ecom/index.html', context)
    response.set_cookie('product_ids', product_ids)  # Store in browser

    return response

def cart_view(request):
    """
    Display cart contents from cookies:
    - Parse cookie data
    - Query database for product details
    - Calculate totals
    """
    products = None
    total = 0

    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids != "":
            # Convert cookie string to list
            product_id_in_cart = product_ids.split('|')
            # Query products by IDs
            products = models.Product.objects.all().filter(id__in=product_id_in_cart)

            # Calculate total price
            for p in products:
                total = total + p.price

    return render(request, 'ecom/cart.html', {
        'products': products,
        'total': total
    })
            </div>

            <h3>Session-Based Authentication</h3>
            <div class="code-block">
# Django automatically handles sessions for authentication
# Session data is stored server-side, only session ID in cookie

# Login creates session
def afterlogin_view(request):
    """
    Post-login processing:
    - request.user contains authenticated user
    - Session automatically created by Django
    """
    if is_customer(request.user):
        return redirect('customer-home')
    else:
        return redirect('admin-dashboard')

# Access user data in any view
@login_required
def my_profile_view(request):
    """
    Session-based user access:
    - request.user.id gives current user ID
    - No need to pass user data manually
    """
    customer = models.Customer.objects.get(user_id=request.user.id)
    return render(request, 'ecom/my_profile.html', {'customer': customer})
            </div>

            <div class="implementation-box">
                <h4>Cookie vs Session Usage:</h4>
                <ul>
                    <li><strong>Cookies:</strong> Shopping cart (client-side, persistent)</li>
                    <li><strong>Sessions:</strong> User authentication (server-side, secure)</li>
                    <li><strong>Cookie Benefits:</strong> No server storage, works without login</li>
                    <li><strong>Session Benefits:</strong> Secure, server-controlled, automatic cleanup</li>
                </ul>
            </div>
        </section>

        <section id="file-handling">
            <h2>8. File Upload & Static Files</h2>

            <div class="concept-box">
                <h3>Django File Handling</h3>
                <p>Django provides robust file upload capabilities with automatic file management and serving.</p>
            </div>

            <h3>File Upload Configuration</h3>
            <div class="code-block">
# settings.py - File handling configuration
import os

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
STATIC_DIR = os.path.join(BASE_DIR, 'static')

# Static files configuration
STATIC_URL = '/static/'
STATICFILES_DIRS = [STATIC_DIR,]

# Media files configuration (user uploads)
MEDIA_ROOT = os.path.join(BASE_DIR, 'static')

# File upload paths in models
class Customer(models.Model):
    profile_pic = models.ImageField(
        upload_to='profile_pic/CustomerProfilePic/',  # Upload path
        null=True,
        blank=True
    )

class Product(models.Model):
    product_image = models.ImageField(
        upload_to='product_image/',  # Upload path
        null=True,
        blank=True
    )
            </div>

            <h3>File Upload in Forms</h3>
            <div class="code-block">
# Form handling with file uploads
def customer_signup_view(request):
    if request.method == 'POST':
        userForm = forms.CustomerUserForm(request.POST)
        # Include request.FILES for file uploads
        customerForm = forms.CustomerForm(request.POST, request.FILES)

        if userForm.is_valid() and customerForm.is_valid():
            user = userForm.save()
            customer = customerForm.save(commit=False)
            customer.user = user
            customer.save()  # File automatically saved to upload_to path

# Template file upload form
# templates/ecom/customersignup.html
&lt;form method="POST" enctype="multipart/form-data"&gt;
    {% csrf_token %}
    {{ customerForm.as_p }}
    &lt;input type="submit" value="Sign Up"&gt;
&lt;/form&gt;
            </div>

            <h3>Static File Serving</h3>
            <div class="code-block">
<!-- Template static file usage -->
{% load static %}

<!-- CSS files -->
&lt;link rel="stylesheet" href="{% static 'css/style.css' %}"&gt;

<!-- JavaScript files -->
&lt;script src="{% static 'js/script.js' %}"&gt;&lt;/script&gt;

<!-- Images -->
&lt;img src="{% static 'images/logo.png' %}" alt="Logo"&gt;

<!-- User uploaded files -->
&lt;img src="{% static customer.profile_pic.url %}" alt="Profile"&gt;
&lt;img src="{% static product.product_image.url %}" alt="Product"&gt;
            </div>

            <div class="warning">
                <strong>Production Considerations:</strong>
                <ul>
                    <li>Use separate media server for user uploads</li>
                    <li>Configure proper file permissions</li>
                    <li>Implement file size and type validation</li>
                    <li>Use CDN for static files</li>
                </ul>
            </div>
        </section>

        <section id="email-integration">
            <h2>9. Email Integration</h2>

            <div class="concept-box">
                <h3>Django Email Framework</h3>
                <p>Django provides a comprehensive email framework for sending emails through various backends.</p>
            </div>

            <h3>Email Configuration</h3>
            <div class="code-block">
# settings.py - Email configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_USE_TLS = True
EMAIL_PORT = 587
EMAIL_HOST_USER = '<EMAIL>'  # Sender email
EMAIL_HOST_PASSWORD = 'xyz'  # App password
EMAIL_RECEIVING_USER = ['<EMAIL>']  # Recipient emails

# Security note: Use environment variables in production
import os
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
            </div>

            <h3>Contact Form Email Implementation</h3>
            <div class="code-block">
from django.core.mail import send_mail
from django.conf import settings

def contactus_view(request):
    """
    Contact form with email sending:
    - Form validation
    - Email composition
    - SMTP sending
    - Error handling
    """
    sub = forms.ContactusForm()

    if request.method == 'POST':
        sub = forms.ContactusForm(request.POST)

        if sub.is_valid():
            # Extract form data
            email = sub.cleaned_data['Email']
            name = sub.cleaned_data['Name']
            message = sub.cleaned_data['Message']

            # Compose email
            subject = str(name) + ' || ' + str(email)

            # Send email
            send_mail(
                subject,                           # Email subject
                message,                          # Email body
                settings.EMAIL_HOST_USER,         # From email
                settings.EMAIL_RECEIVING_USER,    # To email list
                fail_silently=False              # Raise exceptions
            )

            return render(request, 'ecom/contactussuccess.html')

    return render(request, 'ecom/contactus.html', {'form': sub})
            </div>

            <div class="highlight">
                <strong>Email Features Implemented:</strong>
                <ul>
                    <li><strong>SMTP Backend:</strong> Gmail SMTP configuration</li>
                    <li><strong>Form Integration:</strong> Contact form with email sending</li>
                    <li><strong>Error Handling:</strong> fail_silently parameter control</li>
                    <li><strong>Security:</strong> App passwords for Gmail authentication</li>
                </ul>
            </div>
        </section>

        <section id="payment-integration">
            <h2>10. Payment Gateway Integration</h2>

            <div class="concept-box">
                <h3>Stripe Payment Integration</h3>
                <p>The project integrates Stripe for secure payment processing with checkout sessions and webhooks.</p>
            </div>

            <h3>Stripe Configuration</h3>
            <div class="code-block">
# views.py - Stripe setup
import stripe

# Set Stripe API key (use environment variables in production)
stripe.api_key = "sk_test_51RQoRBQQ3USwvpS7nCGQ4qInZZgs0pzragwWRZcmLNKVGlBFrSJdVeljLygC0e6jYJ1ZKL0abkkuG1XKeP9XVRhk00toa8tTdN"

# settings.py - PayPal configuration (alternative)
PAYPAL_RECEIVER_EMAIL = '<EMAIL>'
PAYPAL_TEST = True
            </div>

            <h3>Checkout Session Implementation</h3>
            <div class="code-block">
def customer_address_view(request):
    """
    Payment processing with Stripe Checkout:
    - Collect shipping information
    - Create Stripe checkout session
    - Handle payment success/failure
    """
    if request.method == 'POST':
        addressForm = forms.AddressForm(request.POST)

        if addressForm.is_valid():
            # Extract shipping information
            email = addressForm.cleaned_data['Email']
            mobile = addressForm.cleaned_data['Mobile']
            address = addressForm.cleaned_data['Address']

            # Get cart products and calculate total
            total = 0
            products = None
            if 'product_ids' in request.COOKIES:
                product_ids = request.COOKIES['product_ids']
                if product_ids != "":
                    product_id_in_cart = product_ids.split('|')
                    products = models.Product.objects.all().filter(id__in=product_id_in_cart)
                    for p in products:
                        total = total + p.price

            # Create line items for Stripe
            line_items = []
            for product in products:
                line_items.append({
                    'price_data': {
                        'currency': 'inr',
                        'unit_amount': product.price * 100,  # Convert to cents
                        'product_data': {
                            'name': product.name,
                            'description': product.description,
                        },
                    },
                    'quantity': 1,
                })

            # Create Stripe customer
            stripe.Customer.create()

            # Create checkout session
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=line_items,
                metadata={
                    'user_email': email,
                    'phone_number': mobile,
                    'address': address,
                },
                mode='payment',
                success_url='http://127.0.0.1:8000/payment-success',
                cancel_url='http://127.0.0.1:8000/cart',
            )

            # Redirect to Stripe checkout
            return redirect(checkout_session.url)
            </div>

            <h3>Payment Success Handling</h3>
            <div class="code-block">
@login_required(login_url='customerlogin')
def payment_success_view(request):
    """
    Post-payment processing:
    - Verify payment success
    - Create order records
    - Clear shopping cart
    - Send confirmation
    """
    customer = models.Customer.objects.get(user_id=request.user.id)

    # Get products from cart
    products = None
    if 'product_ids' in request.COOKIES:
        product_ids = request.COOKIES['product_ids']
        if product_ids != "":
            product_id_in_cart = product_ids.split('|')
            products = models.Product.objects.all().filter(id__in=product_id_in_cart)

    # Get shipping information from cookies
    email = request.COOKIES.get('email')
    mobile = request.COOKIES.get('mobile')
    address = request.COOKIES.get('address')

    # Create order records for each product
    for product in products:
        models.Orders.objects.get_or_create(
            customer=customer,
            product=product,
            status='Pending',
            email=email,
            mobile=mobile,
            address=address
        )

    # Clear cart and shipping data
    response = render(request, 'ecom/payment_success.html')
    response.delete_cookie('product_ids')
    response.delete_cookie('email')
    response.delete_cookie('mobile')
    response.delete_cookie('address')

    return response
            </div>

            <div class="implementation-box">
                <h4>Payment Integration Features:</h4>
                <ul>
                    <li><strong>Stripe Checkout:</strong> Secure hosted payment page</li>
                    <li><strong>Metadata:</strong> Custom data passed to Stripe</li>
                    <li><strong>Currency Conversion:</strong> Price conversion to cents</li>
                    <li><strong>Order Creation:</strong> Automatic order generation after payment</li>
                    <li><strong>Cart Cleanup:</strong> Cookie deletion after successful payment</li>
                </ul>
            </div>
        </section>

        <section id="admin-interface">
            <h2>11. Admin Interface Customization</h2>

            <div class="concept-box">
                <h3>Django Admin Framework</h3>
                <p>Django provides a powerful admin interface that can be customized for content management.</p>
            </div>

            <h3>Model Registration</h3>
            <div class="code-block">
# admin.py - Register models with admin
from django.contrib import admin
from .models import Customer, Product, Orders, Feedback

class CustomerAdmin(admin.ModelAdmin):
    """
    Customize Customer admin interface
    """
    list_display = ['get_name', 'mobile', 'address']  # Columns to display
    search_fields = ['user__first_name', 'user__last_name', 'mobile']
    list_filter = ['user__date_joined']

    def get_name(self, obj):
        return obj.get_name
    get_name.short_description = 'Full Name'

class ProductAdmin(admin.ModelAdmin):
    """
    Customize Product admin interface
    """
    list_display = ['name', 'price', 'description']
    search_fields = ['name', 'description']
    list_filter = ['price']
    ordering = ['name']

class OrderAdmin(admin.ModelAdmin):
    """
    Customize Orders admin interface
    """
    list_display = ['customer', 'product', 'status', 'order_date']
    list_filter = ['status', 'order_date']
    search_fields = ['customer__user__first_name', 'product__name']
    date_hierarchy = 'order_date'

# Register models with custom admin classes
admin.site.register(Customer, CustomerAdmin)
admin.site.register(Product, ProductAdmin)
admin.site.register(Orders, OrderAdmin)
admin.site.register(Feedback)
            </div>

            <h3>Custom Admin Views</h3>
            <div class="code-block">
# Custom admin dashboard implementation
@login_required(login_url='adminlogin')
def admin_dashboard_view(request):
    """
    Custom admin dashboard with statistics:
    - Count aggregations
    - Recent orders display
    - Related object queries
    """
    # Calculate statistics
    customercount = models.Customer.objects.all().count()
    productcount = models.Product.objects.all().count()
    ordercount = models.Orders.objects.all().count()

    # Get recent orders with related data
    orders = models.Orders.objects.all()
    ordered_products = []
    ordered_bys = []

    for order in orders:
        # Use select_related for optimization
        ordered_product = models.Product.objects.filter(id=order.product.id)
        ordered_by = models.Customer.objects.filter(id=order.customer.id)
        ordered_products.append(ordered_product)
        ordered_bys.append(ordered_by)

    context = {
        'customercount': customercount,
        'productcount': productcount,
        'ordercount': ordercount,
        'data': zip(ordered_products, ordered_bys, orders),
    }

    return render(request, 'ecom/admin_dashboard.html', context=context)
            </div>

            <div class="highlight">
                <strong>Admin Customization Features:</strong>
                <ul>
                    <li><strong>List Display:</strong> Custom column display in admin lists</li>
                    <li><strong>Search & Filter:</strong> Built-in search and filtering capabilities</li>
                    <li><strong>Custom Methods:</strong> Display computed fields in admin</li>
                    <li><strong>Date Hierarchy:</strong> Date-based navigation for orders</li>
                </ul>
            </div>
        </section>

        <section id="security">
            <h2>14. Security Implementation</h2>

            <div class="concept-box">
                <h3>Django Security Features</h3>
                <p>Django includes many built-in security features that are implemented in this project.</p>
            </div>

            <h3>CSRF Protection</h3>
            <div class="code-block">
<!-- All forms include CSRF token -->
&lt;form method="POST"&gt;
    {% csrf_token %}  <!-- Prevents Cross-Site Request Forgery -->
    {{ form.as_p }}
    &lt;input type="submit" value="Submit"&gt;
&lt;/form&gt;

# settings.py - CSRF middleware enabled by default
MIDDLEWARE = [
    'django.middleware.csrf.CsrfViewMiddleware',  # CSRF protection
    # ... other middleware
]
            </div>

            <h3>Authentication Security</h3>
            <div class="code-block">
# Password hashing
def customer_signup_view(request):
    if userForm.is_valid():
        user = userForm.save()
        user.set_password(user.password)  # Automatic password hashing
        user.save()

# Access control decorators
@login_required(login_url='customerlogin')  # Require authentication
@user_passes_test(is_customer)  # Role-based access control
def customer_home_view(request):
    pass

# settings.py - Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
            </div>

            <div class="warning">
                <strong>Security Considerations for Production:</strong>
                <ul>
                    <li>Set DEBUG=False</li>
                    <li>Use environment variables for secrets</li>
                    <li>Configure ALLOWED_HOSTS</li>
                    <li>Enable HTTPS</li>
                    <li>Use secure session cookies</li>
                    <li>Implement rate limiting</li>
                    <li>Regular security updates</li>
                </ul>
            </div>
        </section>

        <section id="performance">
            <h2>15. Performance Optimization</h2>

            <div class="concept-box">
                <h3>Django Performance Best Practices</h3>
                <p>Several performance optimization techniques are demonstrated in this project.</p>
            </div>

            <h3>Database Query Optimization</h3>
            <div class="code-block">
# Efficient queries with select_related and prefetch_related
def admin_view_booking_view(request):
    """
    Optimized order display:
    - Reduce database queries
    - Use select_related for foreign keys
    """
    # Instead of multiple queries in loop
    orders = models.Orders.objects.select_related('customer', 'product').all()

    # Or use prefetch_related for reverse foreign keys
    customers = models.Customer.objects.prefetch_related('orders_set').all()

# Use filter() instead of get() when possible
products = models.Product.objects.filter(id__in=product_id_in_cart)

# Use exists() for boolean checks
if models.Customer.objects.filter(user=request.user).exists():
    # More efficient than try/except with get()
    pass
            </div>

            <h3>Template Optimization</h3>
            <div class="code-block">
<!-- Template inheritance reduces code duplication -->
{% extends 'ecom/customer_base.html' %}

<!-- Load static files once -->
{% load static %}

<!-- Use template fragments for reusable components -->
{% include "ecom/navbar.html" %}

<!-- Optimize loops with forloop variables -->
{% for product in products %}
    {% if forloop.counter|divisibleby:"3" %}
        &lt;/div&gt;&lt;div class="row"&gt;
    {% endif %}
{% endfor %}
            </div>

            <div class="implementation-box">
                <h4>Performance Features Implemented:</h4>
                <ul>
                    <li><strong>Cookie-based Cart:</strong> Reduces database load</li>
                    <li><strong>Template Inheritance:</strong> Reduces code duplication</li>
                    <li><strong>Static File Optimization:</strong> Proper static file handling</li>
                    <li><strong>Query Optimization:</strong> Efficient database queries</li>
                </ul>
            </div>
        </section>

        <section id="conclusion">
            <h2>Conclusion</h2>

            <div class="highlight">
                <h3>Django Concepts Covered</h3>
                <p>This e-commerce project demonstrates comprehensive implementation of Django's core concepts:</p>
                <ul>
                    <li><strong>MVT Architecture:</strong> Proper separation of concerns</li>
                    <li><strong>ORM & Models:</strong> Database design and relationships</li>
                    <li><strong>Views & URLs:</strong> Request handling and routing</li>
                    <li><strong>Templates:</strong> Dynamic HTML generation</li>
                    <li><strong>Forms:</strong> Data validation and processing</li>
                    <li><strong>Authentication:</strong> User management and security</li>
                    <li><strong>File Handling:</strong> Upload and static file management</li>
                    <li><strong>Third-party Integration:</strong> Payment gateways and email</li>
                    <li><strong>Admin Interface:</strong> Content management system</li>
                    <li><strong>Security:</strong> CSRF protection and access control</li>
                </ul>
            </div>

            <div class="concept-box">
                <h3>Learning Outcomes</h3>
                <p>By studying this implementation, developers will understand:</p>
                <ul>
                    <li>How to structure a Django project properly</li>
                    <li>Best practices for model design and relationships</li>
                    <li>Effective use of Django's built-in features</li>
                    <li>Integration patterns for external services</li>
                    <li>Security considerations for web applications</li>
                    <li>Performance optimization techniques</li>
                </ul>
            </div>

            <p style="text-align: center; margin-top: 30px; font-style: italic; color: #6b7280;">
                <strong>Technical Documentation Version 1.0</strong><br>
                Django E-Commerce Project Implementation Guide<br>
                Last Updated: December 2024
            </p>
        </section>
    </div>
</body>
</html>
